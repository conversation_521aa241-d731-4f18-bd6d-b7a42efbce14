#include <Arduino.h>
#include <SPI.h>
#include <Ethernet.h>
#include <Update.h>
#include <ArduinoOTA.h>

#define ETH_MISO 13
#define ETH_SCK 12
#define ETH_MOSI 11
#define ETH_CS 10
// #define ETH_RST 15 // 9 gateway
#define ETH_RST 9 // 15 kaifaban
#define ETH_INT 14

#define PIN_SPI_SS ETH_CS
#define PIN_ETHERNET_RESET ETH_RST

// OTA server configuration
const char* OTA_SERVER = "th.ota.roulink.com";
const int OTA_PORT = 80;
const char* OTA_PATH = "/download/firmware.bin";

// Global variables
bool otaInProgress = false;
size_t otaProgress = 0;
size_t otaTotal = 0;

void getMacAddr(uint8_t *dmac) {
  assert(esp_efuse_mac_get_default(dmac) == ESP_OK);
}
const size_t CHUNK_SIZE = 5 * 1024; // 16KB per chunk
size_t currentOffset = 0;
size_t totalSize = 0;
size_t partSize = 0;
bool firstChunk = true;
// Download a single chunk of firmware
bool downloadChunk() {
  Serial0.printf("Downloading chunk at offset %d...\n", currentOffset);

  EthernetClient client;
  int ret = client.connect(OTA_SERVER, OTA_PORT);
  Serial0.printf("ret = %d\n", ret);
  if (!ret) {
    Serial0.printf("Unable to connect to %s:%d\n", OTA_SERVER, OTA_PORT);
    return false;
  }

  Serial0.printf("Connected to %s:%d\n", OTA_SERVER, OTA_PORT);

  // Send HTTP GET request with Range header
  Serial0.printf("Sending HTTP request for bytes %d-%d\n", currentOffset, currentOffset + CHUNK_SIZE - 1);
  client.print("GET ");
  client.print(OTA_PATH);
  client.println(" HTTP/1.1");
  client.print("Host: ");
  client.println(OTA_SERVER);
  client.printf("Range: bytes=%d-%d\r\n", currentOffset, currentOffset + CHUNK_SIZE - 1);
  client.println("Connection: close");
  client.println();

  Serial0.println("HTTP request sent, waiting for response...");

  // 等待一小段时间让请求发送完成
  delay(50);

  // Wait for response and parse headers
  unsigned long timeout = millis() + 15000; // 15 second timeout
  bool headersReceived = false;
  unsigned long lastCheck = millis();

  Serial0.printf("Waiting for response, timeout in %d ms\n", 15000);

  while (client.connected() && millis() < timeout) {
    // 每10秒打印一次状态（减少输出频率）
    if (millis() - lastCheck > 10000) {
      Serial0.printf("Still waiting... connected: %d, available: %d, time left: %d ms\n",
                     client.connected(), client.available(), timeout - millis());
      lastCheck = millis();
    }

    if (client.available()) {
      String line = client.readStringUntil('\n');
      line.trim(); // Remove whitespace and \r\n
      Serial0.println("Header: " + line);

      // Parse Content-Range header
      if (line.startsWith("Content-Range:")) {
        int dashPos = line.lastIndexOf('-');
        int slashPos = line.lastIndexOf('/');
        //获取当前块的结束位置
        if (dashPos > 0 && slashPos > dashPos) {
          partSize = line.substring(dashPos + 1, slashPos).toInt();
          Serial0.printf("Current chunk end: %d bytes\n", partSize);
        }
        //获取总长度（仅在第一次获取）
        if (slashPos > 0 && firstChunk) {
          totalSize = line.substring(slashPos + 1).toInt();
          Serial0.printf("Total file size: %d bytes\n", totalSize);
          firstChunk = false;
        }
      }
      // If empty line, headers end
      if (line.length() == 0) {
        Serial0.println("--- HTTP headers end ---");
        headersReceived = true;
        break;
      }
    }
    delay(2); // 小延迟避免CPU占用过高
  }

  // 检查是否成功接收到响应头
  if (!headersReceived) {
    Serial0.printf("Timeout waiting for HTTP headers. Connected: %d, Available: %d\n",
                   client.connected(), client.available());
    client.stop();
    return false;
  }

  // 开始读取数据到缓冲区
  uint8_t buffer[CHUNK_SIZE];
  size_t bytesRead = 0;
  size_t actualRead = 0;

  Serial0.printf("Reading chunk data...\n");

  // 读取数据直到缓冲区满或连接断开
  unsigned long dataTimeout = millis() + 10000; // 10秒数据读取超时
  while (bytesRead < CHUNK_SIZE && client.connected() && millis() < dataTimeout) {
    if (client.available()) {
      size_t available = client.available();
      size_t toRead = min(available, CHUNK_SIZE - bytesRead);

      actualRead = client.read(buffer + bytesRead, toRead);
      bytesRead += actualRead;

      // Serial0.printf("Read %d bytes, total: %d/%d\n", actualRead, bytesRead, CHUNK_SIZE);

      // 重置超时时间，因为我们收到了数据
      dataTimeout = millis() + 10000;
    }
    delay(1); // 避免CPU占用过高
  }

  // 检查是否因为超时而退出
  if (millis() >= dataTimeout) {
    Serial0.println("Timeout reading chunk data");
    client.stop();
    return false;
  }

  Serial0.printf("Chunk reading complete: %d bytes\n", bytesRead);

  // 写入数据到Update
  size_t written = Update.write(buffer, bytesRead);

  if (written > 0) {
    if(written != bytesRead){
      Serial0.printf("Write size mismatch! %u != %u\n", written, bytesRead);
      client.stop();
      return false;
    }

    currentOffset += bytesRead;
    Serial0.printf("Successfully wrote %d bytes, total progress: %d/%d (%.1f%%)\n",
                   bytesRead, currentOffset, totalSize,
                   (float)currentOffset / totalSize * 100.0);
  } else {
    Serial0.printf("Write ERROR: %s\n", Update.errorString());
    client.stop();
    return false;
  }

  client.stop();
  Serial0.println("Chunk download completed");
  return true;
}

// Connect to OTA server and download complete firmware
bool connectToOtaServer() {
  Serial0.println("Starting firmware download...");

  // 重置下载状态
  currentOffset = 0;
  totalSize = 0;
  firstChunk = true;

  // 开始Update过程
  if (!Update.begin(UPDATE_SIZE_UNKNOWN)) {
    Serial0.printf("Update.begin() failed: %s\n", Update.errorString());
    return false;
  }

  // 循环下载所有数据块
  int retryCount = 0;
  const int maxRetries = 100;

  while (true) {
    if (!downloadChunk()) {
      retryCount++;
      Serial0.printf("Failed to download chunk (attempt %d/%d)\n", retryCount, maxRetries);

      if (retryCount >= maxRetries) {
        Serial0.println("Max retries reached, aborting...");
        Update.abort();
        return false;
      }

      // 等待更长时间再重试
      Serial0.println("Waiting 1 second before retry...");
      delay(1000);
      continue;
    }

    // 重置重试计数器
    retryCount = 0;

    // 检查是否下载完成
    if (currentOffset >= totalSize) {
      Serial0.println("All chunks downloaded successfully!");
      break;
    }

    // 延迟避免服务器压力
    Serial0.printf("Progress: %d/%d bytes (%.1f%%), waiting 200ms before next chunk...\n",
                   currentOffset, totalSize, (float)currentOffset / totalSize * 100.0);
    delay(200);
  }

  // 完成Update过程
  if (Update.end(true)) {
    Serial0.println("Update completed successfully!");
    Serial0.println("Restarting in 3 seconds...");
    delay(3000);
    ESP.restart();
    return true;
  } else {
    Serial0.printf("Update.end() failed: %s\n", Update.errorString());
    return false;
  }
}


void setup() {
  Serial0.begin(115200);
  // Initialize SX1262 with default settings
  Serial0.print(F("OTA thread start\n"));

  pinMode(PIN_ETHERNET_RESET, OUTPUT);
  digitalWrite(PIN_ETHERNET_RESET, LOW); // Reset time
  delay(100);
  digitalWrite(PIN_ETHERNET_RESET, HIGH); // Reset time

  Ethernet.init(PIN_SPI_SS);
  SPI.begin(ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  Serial0.printf("SPI.begin(SCK=%d, MISO=%d, MOSI=%d, NSS=%d\n", ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  SPI.setFrequency(8000000); // 提高SPI频率到8MHz

  int status = 0;

  uint8_t mac[6];
  getMacAddr(mac);
  mac[0] &= 0xfe;

  Serial0.printf("Start Ethernet DHCP\n");
  status = Ethernet.begin(mac);

  if (status == 0) {
    if (Ethernet.hardwareStatus() == EthernetNoHardware) {
        Serial0.printf("Ethernet shield was not found\n");
        return;
    } else if (Ethernet.linkStatus() == LinkOFF) {
        Serial0.printf("Ethernet cable is not connected\n");
        return;
    } else {
        Serial0.printf("Unknown Ethernet error\n");
        return;
    }
} else {
    Serial0.printf("Local IP %u.%u.%u.%u\n", Ethernet.localIP()[0], Ethernet.localIP()[1], Ethernet.localIP()[2],
             Ethernet.localIP()[3]);
    Serial0.printf("Subnet Mask %u.%u.%u.%u\n", Ethernet.subnetMask()[0], Ethernet.subnetMask()[1], Ethernet.subnetMask()[2],
             Ethernet.subnetMask()[3]);
    Serial0.printf("Gateway IP %u.%u.%u.%u\n", Ethernet.gatewayIP()[0], Ethernet.gatewayIP()[1], Ethernet.gatewayIP()[2],
             Ethernet.gatewayIP()[3]);
    Serial0.printf("DNS Server IP %u.%u.%u.%u\n", Ethernet.dnsServerIP()[0], Ethernet.dnsServerIP()[1], Ethernet.dnsServerIP()[2],
             Ethernet.dnsServerIP()[3]);

    Serial0.printf("Ethernet connection successful!\n");
    // Connect to OTA server
    Serial0.println("\n=== Starting OTA server connection ===");

    connectToOtaServer();

  // // Port defaults to 3232
  // ArduinoOTA.setPort(80);

  // // Hostname defaults to esp3232-[MAC]
  // ArduinoOTA.setHostname("http://th.ota.roulink.com/download");

  // // No authentication by default
  // ArduinoOTA.setPassword("admin@ULINK");

  // // Password can be set with it's md5 value as well
  // // MD5(admin) = 14c86ca1f4a0b5bc9fe3d6cfbb153a7e
  // ArduinoOTA.setPasswordHash("14c86ca1f4a0b5bc9fe3d6cfbb153a7e");


  //   ArduinoOTA
  //   .onStart([]() {
  //     String type;
  //     if (ArduinoOTA.getCommand() == U_FLASH)
  //       type = "sketch";
  //     else // U_SPIFFS
  //       type = "filesystem";

  //     // NOTE: if updating SPIFFS this would be the place to unmount SPIFFS using SPIFFS.end()
  //     Serial0.println("Start updating " + type);
  //   })
  //   .onEnd([]() {
  //     Serial0.println("\nEnd");
  //   })
  //   .onProgress([](unsigned int progress, unsigned int total) {
  //     Serial0.printf("Progress: %u%%\r", (progress / (total / 100)));
  //   })
  //   .onError([](ota_error_t error) {
  //     Serial0.printf("Error[%u]: ", error);
  //     if (error == OTA_AUTH_ERROR) Serial0.println("Auth Failed");
  //     else if (error == OTA_BEGIN_ERROR) Serial0.println("Begin Failed");
  //     else if (error == OTA_CONNECT_ERROR) Serial0.println("Connect Failed");
  //     else if (error == OTA_RECEIVE_ERROR) Serial0.println("Receive Failed");
  //     else if (error == OTA_END_ERROR) Serial0.println("End Failed");
  //   });

  // ArduinoOTA.begin();

}
}

void loop() {
  // Check Ethernet connection status
  Serial0.println("loop");
  if (Ethernet.linkStatus() == LinkOFF) {
    Serial0.println("Ethernet connection disconnected");
    delay(1000);
    return;
  }

  // Try to connect to OTA server every 30 seconds
  // static unsigned long lastOtaCheck = 0;
  // if (millis() - lastOtaCheck > 30000) {
  //   lastOtaCheck = millis();

  //   Serial0.println("\n=== Periodic OTA server check ===");
  //   if (connectToOtaServer()) {
  //     Serial0.println("OTA server connection test successful");

  //     // If needed, download firmware here
  //     // Example: downloadFirmware("/firmware.bin");
  //   }
  // }

  // ArduinoOTA.handle();

  // Maintain Ethernet connection
  Ethernet.maintain();
  delay(1000);
}

