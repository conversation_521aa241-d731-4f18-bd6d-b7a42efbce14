#include <Arduino.h>
#include <SPI.h>
#include <Ethernet.h>
#include <Update.h>
#include <ArduinoOTA.h>
#include <esp_partition.h>
#include <esp_app_format.h>
#include <esp_ota_ops.h>

#define ETH_MISO 13
#define ETH_SCK 12
#define ETH_MOSI 11
#define ETH_CS 10
// #define ETH_RST 15 // 9 gateway
#define ETH_RST 9 // 15 kaifaban
#define ETH_INT 14

#define PIN_SPI_SS ETH_CS
#define PIN_ETHERNET_RESET ETH_RST

// OTA server configuration
const char* OTA_SERVER = "th.ota.roulink.com";
const int OTA_PORT = 80;
const char* OTA_PATH = "/download/Sec_gate_firmware.bin";



// Global variables
bool otaInProgress = false;
size_t otaProgress = 0;
size_t otaTotal = 0;

void getMacAddr(uint8_t *dmac) {
  assert(esp_efuse_mac_get_default(dmac) == ESP_OK);
}

// Print current app partition information
void printCurrentPartitionInfo() {
  // Get current running partition
  const esp_partition_t* running_partition = esp_ota_get_running_partition();
  if (running_partition != NULL) {
    Serial0.printf("Partition Label: %s\n", running_partition->label);
    Serial0.printf("Partition Type: %d (", running_partition->type);
    switch(running_partition->type) {
      case ESP_PARTITION_TYPE_APP: Serial0.print("APP"); break;
      case ESP_PARTITION_TYPE_DATA: Serial0.print("DATA"); break;
      default: Serial0.printf("UNKNOWN_%d", running_partition->type); break;
    }
    Serial0.println(")");

    Serial0.printf("Partition Subtype: %d (", running_partition->subtype);
    switch(running_partition->subtype) {
      case ESP_PARTITION_SUBTYPE_APP_FACTORY: Serial0.print("FACTORY"); break;
      case ESP_PARTITION_SUBTYPE_APP_OTA_0: Serial0.print("OTA_0"); break;
      case ESP_PARTITION_SUBTYPE_APP_OTA_1: Serial0.print("OTA_1"); break;
      case ESP_PARTITION_SUBTYPE_APP_OTA_2: Serial0.print("OTA_2"); break;
      case ESP_PARTITION_SUBTYPE_APP_OTA_3: Serial0.print("OTA_3"); break;
      default: Serial0.printf("UNKNOWN_%d", running_partition->subtype); break;
    }
    Serial0.println(")");

    Serial0.printf("Partition Address: 0x%08X\n", running_partition->address);
    Serial0.printf("Partition Size: %d bytes (%.2f KB)\n",
                   running_partition->size, running_partition->size / 1024.0);
    Serial0.printf("Partition Encrypted: %s\n", running_partition->encrypted ? "YES" : "NO");
  } else {
    Serial0.println("❌ Failed to get running partition info");
  }
}

// Print current app descriptor information
void printCurrentAppDescriptor() {
  Serial0.println("\n=== Current App Descriptor Information ===");

  // Get current running partition
  const esp_partition_t* running_partition = esp_ota_get_running_partition();
  if (running_partition == NULL) {
    Serial0.println("❌ Failed to get running partition");
    return;
  }

  // Read app descriptor from partition
  esp_app_desc_t app_desc;
  esp_err_t err = esp_ota_get_partition_description(running_partition, &app_desc);

  if (err == ESP_OK) {
    Serial0.printf("App Magic Word: 0x%08X\n", app_desc.magic_word);
    Serial0.printf("App Secure Version: %d\n", app_desc.secure_version);
    Serial0.printf("App Version: %s\n", app_desc.version);
    Serial0.printf("Project Name: %s\n", app_desc.project_name);
    Serial0.printf("Compile Time: %s\n", app_desc.time);
    Serial0.printf("Compile Date: %s\n", app_desc.date);
    Serial0.printf("IDF Version: %s\n", app_desc.idf_ver);

    // Print SHA256 hash
    Serial0.print("App SHA256: ");
    for (int i = 0; i < 32; i++) {
      Serial0.printf("%02x", app_desc.app_elf_sha256[i]);
    }
    Serial0.println();

    Serial0.printf("App Size: %d bytes (%.2f KB)\n",
                   app_desc.app_elf_sha256[0], app_desc.app_elf_sha256[0] / 1024.0);
  } else {
    Serial0.printf("❌ Failed to get app descriptor: %s\n", esp_err_to_name(err));
  }
}

// Print complete current app information
void printCurrentAppInfo() {
  Serial0.println("CURRENT APPLICATION INFORMATION");
  printCurrentPartitionInfo();
  printCurrentAppDescriptor();
  Serial0.println("============================================================");
}
const size_t CHUNK_SIZE = 1024 * 1024; // 16KB per chunk



size_t currentOffset = 0;
size_t totalSize = 0;
size_t partSize = 0;
bool firstChunk = true;
// Connect to OTA server
bool connectToServer(EthernetClient& client) {
  int ret = client.connect(OTA_SERVER, OTA_PORT);
  if (!ret) {
    Serial0.printf("Unable to connect to %s:%d\n", OTA_SERVER, OTA_PORT);
    return false;
  }
  Serial0.printf("Connected to %s:%d\n", OTA_SERVER, OTA_PORT);
  return true;
}

// Send HTTP Range request
void sendHttpRequest(EthernetClient& client) {
  Serial0.printf("Sending HTTP request for bytes %d-%d\n", currentOffset, currentOffset + CHUNK_SIZE - 1);
  client.print("GET ");
  client.print(OTA_PATH);
  client.println(" HTTP/1.1");
  client.print("Host: ");
  client.println(OTA_SERVER);
  client.printf("Range: bytes=%d-%d\r\n", currentOffset, currentOffset + CHUNK_SIZE - 1);
  client.println("Connection: close");
  client.println();
  Serial0.println("HTTP request sent, waiting for response...");
  delay(50); // 等待请求发送完成
}

// Parse HTTP response headers
bool parseHttpHeaders(EthernetClient& client) {
  unsigned long timeout = millis() + 15000; // 15 second timeout
  bool headersReceived = false;
  unsigned long lastCheck = millis();
  bool isFirstLine = true;

  Serial0.printf("Waiting for response, timeout in %d ms\n", 15000);

  while (client.connected() && millis() < timeout) {
    // 每10秒打印一次状态
    if (millis() - lastCheck > 10000) {
      Serial0.printf("Still waiting... connected: %d, available: %d, time left: %d ms\n",
                     client.connected(), client.available(), timeout - millis());
      lastCheck = millis();
    }

    if (client.available()) {
      String line = client.readStringUntil('\n');
      line.trim();
      Serial0.println("Header: " + line);

      // Check HTTP status code in first line
      if (isFirstLine) {
        isFirstLine = false;
        if (line.indexOf("404") != -1) {
          Serial0.println("HTTP 404 - Firmware file not found on server!");
          Serial0.println("OTA update cancelled - file does not exist");
          return false;
        } else if (line.indexOf("200") == -1 && line.indexOf("206") == -1) {
          Serial0.printf(" HTTP Error - Unexpected status: %s\n", line.c_str());
          Serial0.println("OTA update cancelled - server error");
          return false;
        } else {
          Serial0.println("HTTP status OK");
        }
      }

      // Parse Content-Range header
      if (line.startsWith("Content-Range:")) {
        int dashPos = line.lastIndexOf('-');
        int slashPos = line.lastIndexOf('/');
        if (dashPos > 0 && slashPos > dashPos) {
          partSize = line.substring(dashPos + 1, slashPos).toInt();
          Serial0.printf("Current chunk end: %d bytes\n", partSize);
        }
        if (slashPos > 0 && firstChunk) {
          totalSize = line.substring(slashPos + 1).toInt();
          Serial0.printf("Total file size: %d bytes\n", totalSize);
          firstChunk = false;
        }
      }

      if (line.length() == 0) {
        Serial0.println("--- HTTP headers end ---");
        headersReceived = true;
        break;
      }
    }
    delay(2);
  }

  if (!headersReceived) {
    Serial0.printf("Timeout waiting for HTTP headers. Connected: %d, Available: %d\n",
                   client.connected(), client.available());
    return false;
  }
  return true;
}

// Read chunk data from server
bool readChunkData(EthernetClient& client, uint8_t* buffer, size_t& bytesRead) {
  bytesRead = 0;
  Serial0.printf("Reading chunk data...\n");

  unsigned long dataTimeout = millis() + 30000; // 30秒数据读取超时
  while (bytesRead < CHUNK_SIZE && client.connected() && millis() < dataTimeout) {
    if (client.available()) {
      size_t available = client.available();
      size_t toRead = min(available, CHUNK_SIZE - bytesRead);

      size_t actualRead = client.read(buffer + bytesRead, toRead);
      bytesRead += actualRead;

      // 重置超时时间，因为我们收到了数据
      dataTimeout = millis() + 10000;
    }
    delay(1);
  }

  if (millis() >= dataTimeout) {
    Serial0.println("Timeout reading chunk data");
    return false;
  }

  Serial0.printf("Chunk reading complete: %d bytes\n", bytesRead);
  return true;
}

// Write chunk data to Update
bool writeChunkData(uint8_t* buffer, size_t bytesRead) {
  size_t written = Update.write(buffer, bytesRead);

  if (written > 0) {
    if(written != bytesRead){
      Serial0.printf("Write size mismatch! %u != %u\n", written, bytesRead);
      return false;
    }

    currentOffset += bytesRead;
    Serial0.printf("Successfully wrote %d bytes, total progress: %d/%d (%.1f%%)\n",
                   bytesRead, currentOffset, totalSize,
                   (float)currentOffset / totalSize * 100.0);
    return true;
  } else {
    Serial0.printf("Write ERROR: %s\n", Update.errorString());
    return false;
  }
}

// Download a single chunk of firmware
bool downloadChunk() {
  Serial0.printf("Downloading chunk at offset %d...\n", currentOffset);

  EthernetClient client;

  // Step 1: Connect to server
  if (!connectToServer(client)) {
    return false;
  }

  // Step 2: Send HTTP request
  sendHttpRequest(client);

  // Step 3: Parse HTTP headers
  if (!parseHttpHeaders(client)) {
    client.stop();
    return false;
  }

  // Step 4: Allocate buffer
  uint8_t* buffer = (uint8_t*)ps_malloc(CHUNK_SIZE);
  if (buffer == NULL) {
    Serial0.println("PSRAM allocation failed!");
    client.stop();
    return false;
  }

  // Step 5: Read chunk data
  size_t bytesRead = 0;
  if (!readChunkData(client, buffer, bytesRead)) {
    free(buffer);
    client.stop();
    return false;
  }

  // Step 6: Write chunk data
  bool writeSuccess = writeChunkData(buffer, bytesRead);

  // Cleanup
  free(buffer);
  client.stop();

  if (writeSuccess) {
    Serial0.println("Chunk download completed");
    return true;
  } else {
    return false;
  }
}

// Check if firmware file exists on server
bool checkFirmwareExists() {
  Serial0.println("Checking if firmware file exists on server...");

  EthernetClient client;

  // Connect to server
  if (!connectToServer(client)) {
    return false;
  }

  // Send HTTP HEAD request to check file existence
  Serial0.printf("Sending HEAD request to check file: %s\n", OTA_PATH);
  client.print("HEAD ");
  client.print(OTA_PATH);
  client.println(" HTTP/1.1");
  client.print("Host: ");
  client.println(OTA_SERVER);
  client.println("Connection: close");
  client.println();

  delay(50);

  // Parse response headers
  unsigned long timeout = millis() + 10000; // 10 second timeout
  bool headersReceived = false;
  bool fileExists = false;

  while (client.connected() && millis() < timeout) {
    if (client.available()) {
      String line = client.readStringUntil('\n');
      line.trim();
      Serial0.println("Check Header: " + line);

      // Check HTTP status code in first line
      if (line.startsWith("HTTP/")) {
        if (line.indexOf("200") != -1) {
          Serial0.println("Firmware file exists on server");
          fileExists = true;
        } else if (line.indexOf("404") != -1) {
          Serial0.println("Firmware file not found on server (404)");
          fileExists = false;
        } else {
          Serial0.printf("Unexpected HTTP status: %s\n", line.c_str());
          fileExists = false;
        }
        headersReceived = true;
        break;
      }
    }
    delay(2);
  }

  client.stop();

  if (!headersReceived) {
    Serial0.println("❌ Timeout waiting for server response");
    return false;
  }

  return fileExists;
}

// Download complete firmware from server
bool downloadFirmware() {
  Serial0.println("Starting firmware download...");

  // 重置下载状态
  currentOffset = 0;
  totalSize = 0;
  firstChunk = true;

  // 开始Update过程
  if (!Update.begin(UPDATE_SIZE_UNKNOWN)) {
    Serial0.printf("Update.begin() failed: %s\n", Update.errorString());
    return false;
  }

  // 循环下载所有数据块
  int retryCount = 0;
  const int maxRetries = 100;

  while (true) {
    if (!downloadChunk()) {
      retryCount++;
      Serial0.printf("Failed to download chunk (attempt %d/%d)\n", retryCount, maxRetries);

      if (retryCount >= maxRetries) {
        Serial0.println("Max retries reached, aborting...");
        Update.abort();
        return false;
      }

      // 等待更长时间再重试
      Serial0.println("Waiting 1 second before retry...");
      delay(1000);
      continue;
    }

    // 重置重试计数器
    retryCount = 0;

    // 检查是否下载完成
    if (currentOffset >= totalSize) {
      Serial0.println("All chunks downloaded successfully!");
      break;
    }

    // 延迟避免服务器压力
    Serial0.printf("Progress: %d/%d bytes (%.1f%%), waiting 200ms before next chunk...\n",
                   currentOffset, totalSize, (float)currentOffset / totalSize * 100.0);
    delay(200);
  }

  // 完成Update过程
  if (Update.end(true)) {
    Serial0.println("Update completed successfully!");
    Serial0.println("Restarting in 3 seconds...");
    delay(3000);
    ESP.restart();
    return true;
  } else {
    Serial0.printf("Update.end() failed: %s\n", Update.errorString());
    return false;
  }
}

// Main OTA process - check and download if file exists
bool performOtaUpdate() {
  Serial0.println("\n=== Starting OTA Update Process ===");

  // Step 1: Check if firmware file exists
  if (!checkFirmwareExists()) {
    Serial0.println("❌ OTA cancelled - firmware file not available");
    return false;
  }

  // Step 2: Download firmware
  Serial0.println("✅ Firmware file found, starting download...");
  return downloadFirmware();
}


// Initialize system and check PSRAM
void initializeSystem() {
  Serial0.begin(115200);
  Serial0.print(F("OTA thread start\n"));

  // Check PSRAM status
  Serial0.printf("PSRAM found: %s\n", psramFound() ? "YES" : "NO");
  if (psramFound()) {
    Serial0.printf("PSRAM size: %d bytes\n", ESP.getPsramSize());
    Serial0.printf("Free PSRAM: %d bytes\n", ESP.getFreePsram());
  }
}

// Initialize Ethernet hardware
void initializeEthernet() {
  pinMode(PIN_ETHERNET_RESET, OUTPUT);
  digitalWrite(PIN_ETHERNET_RESET, LOW);
  delay(100);
  digitalWrite(PIN_ETHERNET_RESET, HIGH);

  Ethernet.init(PIN_SPI_SS);
  SPI.begin(ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  Serial0.printf("SPI.begin(SCK=%d, MISO=%d, MOSI=%d, NSS=%d\n", ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
  SPI.setFrequency(8000000); // 提高SPI频率到8MHz
}

// Start Ethernet connection with DHCP
bool startEthernetConnection() {
  uint8_t mac[6];
  getMacAddr(mac);
  mac[0] &= 0xfe;

  Serial0.printf("Start Ethernet DHCP\n");
  int status = Ethernet.begin(mac);

  return (status != 0);
}

// Handle Ethernet connection errors
void handleEthernetError() {
  if (Ethernet.hardwareStatus() == EthernetNoHardware) {
    Serial0.printf("Ethernet shield was not found\n");
  } else if (Ethernet.linkStatus() == LinkOFF) {
    Serial0.printf("Ethernet cable is not connected\n");
  } else {
    Serial0.printf("Unknown Ethernet error\n");
  }
}

// Print network information
void printNetworkInfo() {
  Serial0.printf("Local IP %u.%u.%u.%u\n", Ethernet.localIP()[0], Ethernet.localIP()[1], Ethernet.localIP()[2], Ethernet.localIP()[3]);
  Serial0.printf("Subnet Mask %u.%u.%u.%u\n", Ethernet.subnetMask()[0], Ethernet.subnetMask()[1], Ethernet.subnetMask()[2], Ethernet.subnetMask()[3]);
  Serial0.printf("Gateway IP %u.%u.%u.%u\n", Ethernet.gatewayIP()[0], Ethernet.gatewayIP()[1], Ethernet.gatewayIP()[2], Ethernet.gatewayIP()[3]);
  Serial0.printf("DNS Server IP %u.%u.%u.%u\n", Ethernet.dnsServerIP()[0], Ethernet.dnsServerIP()[1], Ethernet.dnsServerIP()[2], Ethernet.dnsServerIP()[3]);
  Serial0.printf("Ethernet connection successful!\n");
}

void setup() {
  // Step 1: Initialize system
  initializeSystem();

  // Step 2: Print current app information
  printCurrentAppInfo();

  // Step 3: Initialize Ethernet hardware
  initializeEthernet();

  // Step 4: Start Ethernet connection
  if (!startEthernetConnection()) {
    handleEthernetError();
    return;
  }

  // Step 5: Print network information
  printNetworkInfo();

  // Step 6: Perform initial OTA check
  Serial0.println("\n=== Initial OTA Check ===");
  performOtaUpdate();
}

void loop() {
  // Check Ethernet connection status
  if (Ethernet.linkStatus() == LinkOFF) {
    Serial0.println("Ethernet connection disconnected");
    delay(1000);
    return;
  }

  // Periodic OTA check every 30 seconds
  static unsigned long lastOtaCheck = 0;
  const unsigned long OTA_CHECK_INTERVAL = 30000; // 30 seconds

  if (millis() - lastOtaCheck > OTA_CHECK_INTERVAL) {
    lastOtaCheck = millis();

    Serial0.println("\n=== Periodic OTA Check ===");

    // Check if firmware file exists and download if available
    if (performOtaUpdate()) {
      // If OTA was successful, the device will restart
      // This code should not be reached
      Serial0.println("OTA completed, device should restart...");
    } else {
      Serial0.println("No OTA update available, continuing normal operation");
    }
  }

  // Maintain Ethernet connection
  Ethernet.maintain();
  delay(1000);
}

