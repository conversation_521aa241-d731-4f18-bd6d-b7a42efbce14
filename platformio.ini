; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:seeed_xiao_esp32s3]
platform = espressif32
board = seeed_xiao_esp32s3
framework = arduino
monitor_speed = 115200
lib_deps =
; debug_tool = esptool
upload_protocol = esptool
debug_speed = 10000
monitor_filters = time
build_flags = 
    ; -DARDUINO_USB_CDC_ON_BOOT=1
    ; -ARDUINO_USB_MODE=0           
lib_ignore = MyLibrary